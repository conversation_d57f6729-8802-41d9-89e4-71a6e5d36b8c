package shipping

import (
	"context"
	"errors"
	"fmt"

	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/jackc/pgtype"
)

// Service handles shipping fee calculations
type Service struct {
	queries *postgres.Queries
	logger  logger.Logger
}

// New creates a new shipping service instance
func New(queries *postgres.Queries, logger logger.Logger) *Service {
	return &Service{
		queries: queries,
		logger:  logger,
	}
}

// ShippingCalculationResult represents the result of a shipping fee calculation
type ShippingCalculationResult struct {
	DistanceKm          float64 `json:"distance_km"`
	ShippingFeeCentavos int32   `json:"shipping_fee_centavos"`
}

// CalculateShippingFee calculates the shipping fee based on company and user address
// Returns the distance and calculated shipping fee in centavos
func (s *Service) CalculateShippingFee(ctx context.Context, companyExternalID, userAddressExternalID string) (*ShippingCalculationResult, error) {
	// Use the sqlc-generated query that handles the entire calculation
	result, err := s.queries.CalculateShippingFeeWithDistance(ctx, postgres.CalculateShippingFeeWithDistanceParams{
		CompanyExternalID:     companyExternalID,
		UserAddressExternalID: userAddressExternalID,
	})
	if err != nil {
		s.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"company_external_id": companyExternalID,
			"address_external_id": userAddressExternalID,
			"error":               err.Error(),
		}).Error("Failed to calculate shipping fee with distance")
		return nil, fmt.Errorf("failed to calculate shipping fee: %w", err)
	}

	// Convert pgtype.Numeric to float64 for distance
	var distance float64
	if result.DistanceKm.Status == pgtype.Present {
		err := result.DistanceKm.AssignTo(&distance)
		if err != nil {
			s.logger.WithContext(ctx).WithFields(map[string]interface{}{
				"company_external_id": companyExternalID,
				"address_external_id": userAddressExternalID,
				"distance_value":      result.DistanceKm,
				"error":               err.Error(),
			}).Error("Failed to convert distance to float64")
			return nil, fmt.Errorf("failed to convert distance value: %w", err)
		}
	}

	s.logger.WithContext(ctx).WithFields(map[string]interface{}{
		"company_external_id":   companyExternalID,
		"address_external_id":   userAddressExternalID,
		"distance_km":           distance,
		"shipping_fee_centavos": result.ShippingFeeCentavos,
	}).Debug("Successfully calculated shipping fee")

	return &ShippingCalculationResult{
		DistanceKm:          distance,
		ShippingFeeCentavos: result.ShippingFeeCentavos,
	}, nil
}

// CalculateShippingFeeForDelivery calculates shipping fee only if delivery mode is "delivery"
// Returns 0 for pickup mode, calculated fee for delivery mode
func (s *Service) CalculateShippingFeeForDelivery(ctx context.Context, companyExternalID, userAddressExternalID, deliveryMode string) (int32, error) {
	if deliveryMode != "delivery" {
		s.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"delivery_mode": deliveryMode,
		}).Debug("Pickup mode selected, returning zero shipping fee")
		return 0, nil
	}

	result, err := s.CalculateShippingFee(ctx, companyExternalID, userAddressExternalID)
	if err != nil {
		return 0, err
	}

	return result.ShippingFeeCentavos, nil
}

// CalculateShippingFeeWithCoordinates calculates shipping fee using coordinates directly
// Returns the distance and calculated shipping fee in centavos
func (s *Service) CalculateShippingFeeWithCoordinates(ctx context.Context, companyExternalID string, userLatitude, userLongitude float64) (*ShippingCalculationResult, error) {
	// Use the sqlc-generated query that handles the entire calculation with coordinates
	result, err := s.queries.CalculateShippingFeeWithCoordinates(ctx, postgres.CalculateShippingFeeWithCoordinatesParams{
		CompanyExternalID: companyExternalID,
		UserLatitude:      userLatitude,
		UserLongitude:     userLongitude,
	})
	if err != nil {
		s.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"company_external_id": companyExternalID,
			"user_latitude":       userLatitude,
			"user_longitude":      userLongitude,
			"error":               err.Error(),
		}).Error("Failed to calculate shipping fee with coordinates")
		return nil, fmt.Errorf("failed to calculate shipping fee: %w", err)
	}

	// Convert pgtype.Numeric to float64 for distance
	var distance float64
	if result.DistanceKm.Status == pgtype.Present {
		err := result.DistanceKm.AssignTo(&distance)
		if err != nil {
			s.logger.WithContext(ctx).WithFields(map[string]interface{}{
				"company_external_id": companyExternalID,
				"user_latitude":       userLatitude,
				"user_longitude":      userLongitude,
				"distance_value":      result.DistanceKm,
				"error":               err.Error(),
			}).Error("Failed to convert distance to float64")
			return nil, fmt.Errorf("failed to convert distance value: %w", err)
		}
	}

	s.logger.WithContext(ctx).WithFields(map[string]interface{}{
		"company_external_id":   companyExternalID,
		"user_latitude":         userLatitude,
		"user_longitude":        userLongitude,
		"distance_km":           distance,
		"shipping_fee_centavos": result.ShippingFeeCentavos,
	}).Debug("Successfully calculated shipping fee with coordinates")

	return &ShippingCalculationResult{
		DistanceKm:          distance,
		ShippingFeeCentavos: result.ShippingFeeCentavos,
	}, nil
}

// CalculateShippingFeeForDeliveryWithCoordinates calculates shipping fee only if delivery mode is "delivery" using coordinates
// Returns 0 for pickup mode, calculated fee for delivery mode
func (s *Service) CalculateShippingFeeForDeliveryWithCoordinates(ctx context.Context, companyExternalID string, userLatitude, userLongitude float64, deliveryMode string) (int32, error) {
	if deliveryMode != "delivery" {
		s.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"delivery_mode": deliveryMode,
		}).Debug("Pickup mode selected, returning zero shipping fee")
		return 0, nil
	}

	result, err := s.CalculateShippingFeeWithCoordinates(ctx, companyExternalID, userLatitude, userLongitude)
	if err != nil {
		return 0, err
	}

	return result.ShippingFeeCentavos, nil
}

// ValidateShippingRateConfiguration validates that a company has proper shipping rate configuration
func (s *Service) ValidateShippingRateConfiguration(ctx context.Context, companyID int32) error {
	rates, err := s.queries.GetCompanyShippingRates(ctx, companyID)
	if err != nil {
		s.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"company_id": companyID,
			"error":      err.Error(),
		}).Error("Failed to get company shipping rates")
		return fmt.Errorf("failed to get shipping rates: %w", err)
	}

	if len(rates) == 0 {
		s.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"company_id": companyID,
		}).Warn("Company has no shipping rates configured")
		return errors.New("company has no shipping rates configured")
	}

	// Check for gaps in distance coverage
	// This is a basic validation - in production you might want more sophisticated checks
	for i := 0; i < len(rates)-1; i++ {
		currentMax := rates[i].DistanceMaxKm
		nextMin := rates[i+1].DistanceMinKm

		var currentMaxFloat, nextMinFloat float64
		if currentMax.Status == pgtype.Present {
			currentMax.AssignTo(&currentMaxFloat)
		}
		if nextMin.Status == pgtype.Present {
			nextMin.AssignTo(&nextMinFloat)
		}

		if currentMaxFloat != nextMinFloat {
			s.logger.WithContext(ctx).WithFields(map[string]interface{}{
				"company_id":   companyID,
				"current_max":  currentMaxFloat,
				"next_min":     nextMinFloat,
				"gap_detected": true,
			}).Warn("Gap detected in shipping rate distance coverage")
		}
	}

	s.logger.WithContext(ctx).WithFields(map[string]interface{}{
		"company_id":  companyID,
		"rates_count": len(rates),
	}).Debug("Shipping rate configuration validated")

	return nil
}

// GetCompanyShippingRates retrieves all shipping rates for a company
func (s *Service) GetCompanyShippingRates(ctx context.Context, companyID int32) ([]postgres.CompanyShippingRate, error) {
	rates, err := s.queries.GetCompanyShippingRates(ctx, companyID)
	if err != nil {
		s.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"company_id": companyID,
			"error":      err.Error(),
		}).Error("Failed to get company shipping rates")
		return nil, fmt.Errorf("failed to get shipping rates: %w", err)
	}

	return rates, nil
}
