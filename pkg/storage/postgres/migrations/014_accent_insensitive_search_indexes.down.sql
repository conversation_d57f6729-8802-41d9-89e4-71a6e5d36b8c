-- Migration Rollback: Remove accent-insensitive search indexes
-- This migration removes the accent-insensitive search indexes created in the up migration

-- =====================================================================================
-- 📌 REMOVE ACCENT-INSENSITIVE FULL-TEXT SEARCH INDEXES
-- =====================================================================================

-- Remove GIN index for accent-insensitive full-text search on product names
DROP INDEX CONCURRENTLY IF EXISTS idx_products_name_unaccent_gin;

-- Remove GIN index for accent-insensitive full-text search on product brands
DROP INDEX CONCURRENTLY IF EXISTS idx_products_brand_unaccent_gin;

-- =====================================================================================
-- 📌 REMOVE ACCENT-INSENSITIVE ILIKE PATTERN INDEXES
-- =====================================================================================

-- Remove B-tree index for accent-insensitive ILIKE patterns on product names
DROP INDEX CONCURRENTLY IF EXISTS idx_products_name_unaccent_ilike;

-- Remove B-tree index for accent-insensitive ILIKE patterns on product brands
DROP INDEX CONCURRENTLY IF EXISTS idx_products_brand_unaccent_ilike;

-- =====================================================================================
-- 📌 REMOVE USER SEARCH ACCENT-INSENSITIVE INDEXES
-- =====================================================================================

-- Remove B-tree index for accent-insensitive user name search
DROP INDEX CONCURRENTLY IF EXISTS idx_users_name_unaccent_ilike;

-- Remove B-tree index for accent-insensitive user email search
DROP INDEX CONCURRENTLY IF EXISTS idx_users_email_unaccent_ilike;

-- =====================================================================================
-- 📌 ROLLBACK NOTES
-- =====================================================================================
-- 1. All indexes are dropped with CONCURRENTLY to avoid blocking production traffic
-- 2. After this rollback, search will revert to accent-sensitive behavior
-- 3. The original search functionality will remain intact via existing indexes
-- 4. Performance may be slightly better due to fewer indexes to maintain
-- =====================================================================================
